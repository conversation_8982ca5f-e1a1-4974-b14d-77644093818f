<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>银行查询功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        h1, h2 {
            color: #333;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .loading {
            color: #666;
            font-style: italic;
        }
        .error {
            color: #dc3545;
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success {
            color: #155724;
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        .search-box {
            margin: 10px 0;
        }
        .search-box input {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            width: 300px;
        }
    </style>
</head>
<body>
    <h1>银行查询功能测试</h1>
    
    <div class="container">
        <h2>对公银行列表</h2>
        <button id="loadCorporateBanks">加载对公银行列表</button>
        <div class="search-box">
            <input type="text" id="corporateSearch" placeholder="搜索银行名称..." />
        </div>
        <div id="corporateResult"></div>
    </div>
    
    <div class="container">
        <h2>个人银行列表</h2>
        <button id="loadPersonalBanks">加载个人银行列表</button>
        <div class="search-box">
            <input type="text" id="personalSearch" placeholder="搜索银行名称..." />
        </div>
        <div id="personalResult"></div>
    </div>
    
    <div class="container">
        <h2>支行查询</h2>
        <div>
            <input type="text" id="bankAliasCode" placeholder="银行别名编码 (如: ICBC)" />
            <input type="text" id="cityCode" placeholder="城市编码 (可选，如: 110100)" />
            <input type="number" id="limit" placeholder="查询数量 (默认20)" min="1" max="200" value="20" />
            <button id="loadBranches">查询支行</button>
        </div>
        <div class="info" style="margin: 10px 0; padding: 10px; background-color: #e7f3ff; border-left: 4px solid #2196F3;">
            <strong>提示：</strong>
            <ul style="margin: 5px 0; padding-left: 20px;">
                <li>银行别名编码：ICBC(工商), CCB(建设), ABC(农业), BOC(中国), CMB(招商)等</li>
                <li>城市编码：110100(北京), 310100(上海), 440100(广州), 440300(深圳)等</li>
                <li>查询数量：1-200之间，默认20</li>
            </ul>
        </div>
        <div id="branchResult"></div>
    </div>

    <script>
        // API基础URL
        const API_BASE = 'http://127.0.0.1:8081/api/bank';
        
        // 全局数据存储
        let corporateBanks = [];
        let personalBanks = [];
        
        // 页面加载完成后绑定事件
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('loadCorporateBanks').addEventListener('click', loadCorporateBanks);
            document.getElementById('loadPersonalBanks').addEventListener('click', loadPersonalBanks);
            document.getElementById('loadBranches').addEventListener('click', loadBranches);
            
            // 搜索功能
            document.getElementById('corporateSearch').addEventListener('input', function() {
                filterBanks(corporateBanks, this.value, 'corporateResult');
            });
            document.getElementById('personalSearch').addEventListener('input', function() {
                filterBanks(personalBanks, this.value, 'personalResult');
            });
        });
        
        // 加载对公银行列表
        async function loadCorporateBanks() {
            const button = document.getElementById('loadCorporateBanks');
            const resultDiv = document.getElementById('corporateResult');
            
            button.disabled = true;
            resultDiv.innerHTML = '<div class="loading">正在加载对公银行列表...</div>';
            
            try {
                const response = await fetch(`${API_BASE}/corporate`);
                const result = await response.json();
                
                if (result.code === 200) {
                    corporateBanks = result.data;
                    displayBanks(corporateBanks, 'corporateResult');
                    resultDiv.insertAdjacentHTML('afterbegin', 
                        `<div class="success">成功加载 ${corporateBanks.length} 家对公银行</div>`);
                } else {
                    resultDiv.innerHTML = `<div class="error">加载失败: ${result.msg}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">请求失败: ${error.message}</div>`;
            } finally {
                button.disabled = false;
            }
        }
        
        // 加载个人银行列表
        async function loadPersonalBanks() {
            const button = document.getElementById('loadPersonalBanks');
            const resultDiv = document.getElementById('personalResult');
            
            button.disabled = true;
            resultDiv.innerHTML = '<div class="loading">正在加载个人银行列表...</div>';
            
            try {
                const response = await fetch(`${API_BASE}/personal`);
                const result = await response.json();
                
                if (result.code === 200) {
                    personalBanks = result.data;
                    displayBanks(personalBanks, 'personalResult');
                    resultDiv.insertAdjacentHTML('afterbegin', 
                        `<div class="success">成功加载 ${personalBanks.length} 家个人银行</div>`);
                } else {
                    resultDiv.innerHTML = `<div class="error">加载失败: ${result.msg}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">请求失败: ${error.message}</div>`;
            } finally {
                button.disabled = false;
            }
        }
        
        // 加载支行列表
        async function loadBranches() {
            const bankAliasCode = document.getElementById('bankAliasCode').value.trim();
            const cityCode = document.getElementById('cityCode').value.trim();
            const limit = document.getElementById('limit').value || 20;
            const button = document.getElementById('loadBranches');
            const resultDiv = document.getElementById('branchResult');

            if (!bankAliasCode) {
                resultDiv.innerHTML = '<div class="error">请输入银行别名编码</div>';
                return;
            }

            button.disabled = true;
            resultDiv.innerHTML = '<div class="loading">正在查询支行列表...</div>';

            try {
                let url = `${API_BASE}/branches/${bankAliasCode}`;
                const params = new URLSearchParams();

                if (cityCode) {
                    params.append('cityCode', cityCode);
                }
                params.append('limit', limit);
                params.append('offset', '0');

                if (params.toString()) {
                    url += `?${params.toString()}`;
                }

                console.log('请求URL:', url);

                const response = await fetch(url);
                const result = await response.json();

                if (result.code === 200) {
                    displayBranches(result.data, 'branchResult');
                    resultDiv.insertAdjacentHTML('afterbegin',
                        `<div class="success">成功查询到 ${result.data.length} 个支行</div>`);
                } else {
                    resultDiv.innerHTML = `<div class="error">查询失败: ${result.msg}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">请求失败: ${error.message}</div>`;
            } finally {
                button.disabled = false;
            }
        }
        
        // 显示银行列表
        function displayBanks(banks, containerId) {
            const container = document.getElementById(containerId);
            
            if (banks.length === 0) {
                container.innerHTML += '<div>暂无数据</div>';
                return;
            }
            
            let html = `
                <table>
                    <thead>
                        <tr>
                            <th>银行名称</th>
                            <th>银行编码</th>
                            <th>银行别名</th>
                            <th>别名编码</th>
                            <th>需要支行</th>
                        </tr>
                    </thead>
                    <tbody>
            `;
            
            banks.forEach(bank => {
                html += `
                    <tr>
                        <td>${bank.accountBank || '-'}</td>
                        <td>${bank.accountBankCode || '-'}</td>
                        <td>${bank.bankAlias || '-'}</td>
                        <td>${bank.bankAliasCode || '-'}</td>
                        <td>${bank.needBankBranch ? '是' : '否'}</td>
                    </tr>
                `;
            });
            
            html += '</tbody></table>';
            container.innerHTML += html;
        }
        
        // 显示支行列表
        function displayBranches(branches, containerId) {
            const container = document.getElementById(containerId);
            
            if (branches.length === 0) {
                container.innerHTML += '<div>暂无支行数据</div>';
                return;
            }
            
            let html = `
                <table>
                    <thead>
                        <tr>
                            <th>支行名称</th>
                            <th>支行编码</th>
                            <th>省份</th>
                            <th>城市</th>
                            <th>银行别名</th>
                        </tr>
                    </thead>
                    <tbody>
            `;
            
            branches.forEach(branch => {
                html += `
                    <tr>
                        <td>${branch.bankBranchName || '-'}</td>
                        <td>${branch.bankBranchId || '-'}</td>
                        <td>${branch.provinceName || '-'}</td>
                        <td>${branch.cityName || '-'}</td>
                        <td>${branch.bankAlias || '-'}</td>
                    </tr>
                `;
            });
            
            html += '</tbody></table>';
            container.innerHTML += html;
        }
        
        // 过滤银行列表
        function filterBanks(banks, searchTerm, containerId) {
            if (!searchTerm) {
                displayBanks(banks, containerId);
                return;
            }
            
            const filtered = banks.filter(bank => 
                (bank.accountBank && bank.accountBank.toLowerCase().includes(searchTerm.toLowerCase())) ||
                (bank.bankAlias && bank.bankAlias.toLowerCase().includes(searchTerm.toLowerCase()))
            );
            
            const container = document.getElementById(containerId);
            container.innerHTML = '';
            displayBanks(filtered, containerId);
        }
    </script>
</body>
</html>
