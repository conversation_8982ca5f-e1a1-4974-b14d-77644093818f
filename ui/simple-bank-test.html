<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>银行查询简单测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        button { padding: 10px 15px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #0056b3; }
        .result { margin-top: 10px; padding: 10px; background: #f8f9fa; border-radius: 3px; }
        .error { background: #f8d7da; color: #721c24; }
        .success { background: #d4edda; color: #155724; }
        input { padding: 8px; margin: 5px; border: 1px solid #ddd; border-radius: 3px; }
    </style>
</head>
<body>
    <h1>银行查询功能测试</h1>
    
    <div class="section">
        <h3>1. 测试对公银行列表</h3>
        <button onclick="testCorporateBanks()">查询对公银行</button>
        <div id="corporate-result" class="result"></div>
    </div>
    
    <div class="section">
        <h3>2. 测试个人银行列表</h3>
        <button onclick="testPersonalBanks()">查询个人银行</button>
        <div id="personal-result" class="result"></div>
    </div>
    
    <div class="section">
        <h3>3. 测试支行查询</h3>
        <input type="text" id="bank-code" placeholder="银行编码 (如: ICBC)" value="ICBC">
        <button onclick="testBranches()">查询支行</button>
        <div id="branch-result" class="result"></div>
    </div>

    <script>
        const API_BASE = 'http://127.0.0.1:8081/api/bank';
        
        async function testCorporateBanks() {
            const resultDiv = document.getElementById('corporate-result');
            resultDiv.innerHTML = '正在查询...';
            resultDiv.className = 'result';
            
            try {
                const response = await fetch(`${API_BASE}/corporate`);
                const result = await response.json();
                
                if (result.code === 200) {
                    resultDiv.innerHTML = `
                        <strong>成功！</strong><br>
                        查询到 ${result.data.length} 家银行<br>
                        示例银行：${result.data.slice(0, 3).map(b => b.accountBank).join(', ')}
                    `;
                    resultDiv.className = 'result success';
                } else {
                    resultDiv.innerHTML = `<strong>失败：</strong>${result.msg}`;
                    resultDiv.className = 'result error';
                }
            } catch (error) {
                resultDiv.innerHTML = `<strong>错误：</strong>${error.message}`;
                resultDiv.className = 'result error';
            }
        }
        
        async function testPersonalBanks() {
            const resultDiv = document.getElementById('personal-result');
            resultDiv.innerHTML = '正在查询...';
            resultDiv.className = 'result';
            
            try {
                const response = await fetch(`${API_BASE}/personal`);
                const result = await response.json();
                
                if (result.code === 200) {
                    resultDiv.innerHTML = `
                        <strong>成功！</strong><br>
                        查询到 ${result.data.length} 家银行<br>
                        示例银行：${result.data.slice(0, 3).map(b => b.accountBank).join(', ')}
                    `;
                    resultDiv.className = 'result success';
                } else {
                    resultDiv.innerHTML = `<strong>失败：</strong>${result.msg}`;
                    resultDiv.className = 'result error';
                }
            } catch (error) {
                resultDiv.innerHTML = `<strong>错误：</strong>${error.message}`;
                resultDiv.className = 'result error';
            }
        }
        
        async function testBranches() {
            const bankCode = document.getElementById('bank-code').value.trim();
            const resultDiv = document.getElementById('branch-result');
            
            if (!bankCode) {
                resultDiv.innerHTML = '<strong>请输入银行编码</strong>';
                resultDiv.className = 'result error';
                return;
            }
            
            resultDiv.innerHTML = '正在查询...';
            resultDiv.className = 'result';
            
            try {
                const url = `${API_BASE}/branches/${bankCode}?limit=10&offset=0`;
                console.log('请求URL:', url);
                
                const response = await fetch(url);
                const result = await response.json();
                
                if (result.code === 200) {
                    resultDiv.innerHTML = `
                        <strong>成功！</strong><br>
                        查询到 ${result.data.length} 个支行<br>
                        示例支行：${result.data.slice(0, 2).map(b => b.bankBranchName).join(', ')}
                    `;
                    resultDiv.className = 'result success';
                } else {
                    resultDiv.innerHTML = `<strong>失败：</strong>${result.msg}`;
                    resultDiv.className = 'result error';
                }
            } catch (error) {
                resultDiv.innerHTML = `<strong>错误：</strong>${error.message}`;
                resultDiv.className = 'result error';
            }
        }
        
        // 页面加载后自动测试对公银行
        window.onload = function() {
            console.log('页面加载完成，开始测试...');
            testCorporateBanks();
        };
    </script>
</body>
</html>
