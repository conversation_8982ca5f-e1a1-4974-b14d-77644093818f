package cn.jiawen.payment.controller;

import cn.jiawen.payment.entity.BankBranchInfo;
import cn.jiawen.payment.entity.BankInfo;
import cn.jiawen.payment.service.WxPayService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;
import vo.AjaxResult;

import javax.annotation.Resource;
import java.util.List;

/**
 * 银行查询控制器
 */
@CrossOrigin
@RestController
@RequestMapping("/api/bank")
@Api(tags = "银行查询接口")
public class BankController {

    private static final Logger log = LoggerFactory.getLogger(BankController.class);

    @Resource
    private WxPayService wxPayService;

    @ApiOperation("查询支持对公业务的银行列表")
    @GetMapping("/corporate")
    public AjaxResult queryCorporateBanks() {

        try {
            log.info("查询支持对公业务的银行列表");

            List<BankInfo> bankList = wxPayService.queryCorporateBanks();

            return AjaxResult.success("查询成功", bankList);

        } catch (Exception e) {
            log.error("查询对公银行列表失败: {}", e.getMessage(), e);
            return AjaxResult.error("查询失败: " + e.getMessage());
        }
    }

    @ApiOperation("查询支持个人业务的银行列表")
    @GetMapping("/personal")
    public AjaxResult queryPersonalBanks() {

        try {
            log.info("查询支持个人业务的银行列表");

            List<BankInfo> bankList = wxPayService.queryPersonalBanks();

            return AjaxResult.success("查询成功", bankList);

        } catch (Exception e) {
            log.error("查询个人银行列表失败: {}", e.getMessage(), e);
            return AjaxResult.error("查询失败: " + e.getMessage());
        }
    }

    @ApiOperation("查询支行列表")
    @GetMapping("/branches/{bankAliasCode}")
    public AjaxResult queryBankBranches(
            @ApiParam(value = "银行别名编码", required = true) @PathVariable String bankAliasCode,
            @ApiParam(value = "城市编码") @RequestParam(required = false) String cityCode,
            @ApiParam(value = "分页偏移量") @RequestParam(required = false) Integer offset,
            @ApiParam(value = "分页限制") @RequestParam(required = false) Integer limit) {

        try {
            log.info("查询支行列表，银行编码：{}, 城市编码：{}", bankAliasCode, cityCode);

            List<BankBranchInfo> branchList = wxPayService.queryBankBranches(bankAliasCode, cityCode, offset, limit);

            return AjaxResult.success("查询成功", branchList);

        } catch (Exception e) {
            log.error("查询支行列表失败: {}", e.getMessage(), e);
            return AjaxResult.error("查询失败: " + e.getMessage());
        }
    }
}
