package cn.jiawen.payment.service;

import cn.jiawen.payment.entity.BankBranchInfo;
import cn.jiawen.payment.entity.BankInfo;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.security.GeneralSecurityException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public interface WxPayService {

    Map<String, Object> nativePay(Long productId) throws Exception;

    void processOrder(HashMap<String, Object> bodyMap) throws GeneralSecurityException;

    void cancelOrder(String orderNo) throws Exception;

    String queryOrder(String orderNo) throws Exception;

    void checkOrderStatus(String orderNo) throws Exception;

    void refund(String orderNo, String reason) throws Exception;

    String queryRefund(String refundNo) throws Exception;

    void processRefund(Map<String, Object> bodyMap) throws Exception;

    void checkRefundStatus(String refundNo) throws Exception;

    String queryBill(String billDate, String type) throws Exception;

    String downloadBill(String billDate, String type) throws Exception;

    String applyment(Map<String, String> body) throws Exception;

    String mediaUpload(MultipartFile file) throws Exception;

    /**
     * 查询支持对公业务的银行列表
     * @return 银行列表
     * @throws Exception Exception
     */
    List<BankInfo> queryCorporateBanks() throws Exception;

    /**
     * 查询支持个人业务的银行列表
     * @return 银行列表
     * @throws Exception Exception
     */
    List<BankInfo> queryPersonalBanks() throws Exception;

    /**
     * 查询支行列表
     * @param bankAliasCode 银行别名编码
     * @param cityCode 城市编码（可选）
     * @param offset 分页偏移量（可选）
     * @param limit 分页限制（可选）
     * @return 支行列表
     * @throws Exception Exception
     */
    List<BankBranchInfo> queryBankBranches(String bankAliasCode, String cityCode, Integer offset, Integer limit) throws Exception;
}
