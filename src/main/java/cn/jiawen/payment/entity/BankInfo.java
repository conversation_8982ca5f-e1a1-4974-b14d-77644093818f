package cn.jiawen.payment.entity;

import lombok.Data;

/**
 * 银行信息实体类
 */
@Data
public class BankInfo {
    
    /**
     * 开户银行编码
     */
    private String accountBankCode;
    
    /**
     * 开户银行
     */
    private String accountBank;
    
    /**
     * 银行别名编码（用于前端展示、检索支行）
     */
    private String bankAliasCode;
    
    /**
     * 银行别名（用于前端展示、检索支行）
     */
    private String bankAlias;
    
    /**
     * 是否需要填写支行
     */
    private Boolean needBankBranch;
}
