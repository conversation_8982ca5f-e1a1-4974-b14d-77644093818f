package cn.jiawen.payment.entity;

import lombok.Data;

/**
 * 银行支行信息实体类
 */
@Data
public class BankBranchInfo {
    
    /**
     * 支行编码
     */
    private String bankBranchId;
    
    /**
     * 支行名称
     */
    private String bankBranchName;
    
    /**
     * 银行别名编码
     */
    private String bankAliasCode;
    
    /**
     * 银行别名
     */
    private String bankAlias;
    
    /**
     * 省份编码
     */
    private String provinceCode;
    
    /**
     * 省份名称
     */
    private String provinceName;
    
    /**
     * 城市编码
     */
    private String cityCode;
    
    /**
     * 城市名称
     */
    private String cityName;
}
